-- 导入必要的库文件
require("TSLib")
ts = require("ts")
json = require("luajson")

-- 初始化
init(1)

-- 设置日志标识
local logTag = "hblog"

-- 初始化随机数种子
math.randomseed(getRndNum())

-- 创建必要的目录
local userPathStr = userPath()
os.execute("mkdir " .. userPathStr .. "/res/360ex")
os.execute("mkdir " .. userPathStr .. "/res/hbbug")

-- 查找文字并点击的函数
function findTextAndClick(text, x1, y1, x2, y2, color, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 85
  end

  -- 保存当前坐标
  local savedX = pyx
  local savedY = pyy or 0
  pyy = savedY
  pyx = savedX

  -- 查找文字
  local foundX, foundY = tsFindText(text, x1, y1, x2, y2, color, similarity)

  -- 如果找到文字则点击
  if foundX > -1 then
    Click(foundX, foundY, 0)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
Fw_click = findTextAndClick

-- OCR文字识别并返回布尔值的函数
function findWordReturnBool(region, expectedText, x1, y1, x2, y2, color, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 85
  end

  -- 使用OCR识别文字
  local recognizedText = tsOcrText(region, x1, y1, x2, y2, color, similarity)

  -- 比较识别的文字是否与期望文字相同
  if recognizedText == expectedText then
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
__Findwordrebool = findWordReturnBool

-- 多色查找并点击的函数
function myFindColorAndClick(colorArray, index)
  -- 从颜色数组中获取指定索引的颜色信息
  local colorInfo = colorArray[index]

  -- 执行多色查找
  local foundX, foundY = findMultiColorInRegionFuzzy(
    colorInfo[1], -- 第一个颜色
    colorInfo[2], -- 第二个颜色
    colorInfo[3], -- 第三个颜色
    colorInfo[4], -- 第四个颜色
    colorInfo[5], -- 第五个颜色
    colorInfo[6], -- 第六个颜色
    colorInfo[7]  -- 第七个颜色
  )

  -- 保存找到的坐标到全局变量
  y = foundY
  x = foundX

  -- 如果找到则点击
  if foundX ~= -1 then
    Click(foundX, foundY)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
My_tfc = myFindColorAndClick

-- 颜色检测函数
function colorCheck(colorData, shouldClick, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end

  -- 检测多色
  local colorFound = multiColor(colorData[2], similarity)

  if colorFound then
    -- 如果需要点击且参数允许点击
    if shouldClick == nil or shouldClick == true then
      local clickX = colorData[2][1][1]
      local clickY = colorData[2][1][2]
      Click(clickX, clickY)
    end
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
CC = colorCheck

-- 字符串分割函数
function stringSplit(inputString, delimiter)
  local result = {}
  local pattern = "[^" .. delimiter .. "]+"

  -- 使用gsub函数分割字符串
  string.gsub(inputString, pattern, function(match)
    table.insert(result, match)
  end)

  return result
end

-- 为了保持兼容性，保留原函数名
my_Split = stringSplit

-- 生成随机数的函数
function randomNumber(min, max)
  return math.random(min, max)
end

-- 为了保持兼容性，保留原函数名
Rnumber = randomNumber

-- 随机睡眠函数
function randomSleep(minTime, maxTime)
  local sleepTime = math.random(minTime, maxTime)
  mSleep(sleepTime)
end

-- 为了保持兼容性，保留原函数名
_Sleep = randomSleep

-- 日志打印函数
function printLog(message)
  -- 如果启用了日志且消息不为空
  if __isnlog__ and message ~= "" then
    nLog(message)
    log(message, logTag)
  end
end

-- 为了保持兼容性，保留原函数名
_print = printLog

-- 模拟触摸点击函数（支持单击和双击）
function simulateTap(x, y, tapType, randomOffset)
  -- 设置默认随机偏移量
  if not randomOffset then
    randomOffset = 5
  end

  -- 添加随机偏移以模拟真实点击
  local offsetX = math.random(-randomOffset, randomOffset)
  local offsetY = math.random(-randomOffset, randomOffset)
  x = x + offsetX
  y = y + offsetY

  if tapType == 2 then
    -- 双击模式
    -- 第一次点击
    touchDown(1, x, y)
    mSleep(math.random(50, 200))
    touchUp(1, x, y)
    mSleep(math.random(50, 150))

    -- 为第二次点击添加新的随机偏移
    offsetX = math.random(-randomOffset, randomOffset)
    offsetY = math.random(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    -- 第二次点击
    touchDown(1, x, y)
    mSleep(math.random(30, 200))
    touchUp(1, x, y)
  else
    -- 单击模式
    touchDown(1, x, y)
    mSleep(math.random(50, 300))
    touchUp(1, x, y)
  end
end

-- 为了保持兼容性，保留原函数名
_tap = simulateTap

-- 多色查找并双击的函数
function findColorAndDoubleClick(colorData, x1, y1, x2, y2, randomOffset, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  -- 如果找到颜色则执行双击
  if foundX ~= -1 then
    DClick(foundX, foundY, randomOffset)
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_DClicke = findColorAndDoubleClick

-- 双击函数（带随机偏移）
function doubleClick(x, y, randomOffset)
  if randomOffset == nil then
    -- 使用默认偏移量5
    -- 第一次点击
    local offsetX = Rnumber(-5, 5)
    local offsetY = Rnumber(-5, 5)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)

    -- 第二次点击（重新计算偏移）
    offsetX = Rnumber(-5, 5)
    offsetY = Rnumber(-5, 5)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)
  else
    -- 使用自定义偏移量
    -- 第一次点击
    local offsetX = Rnumber(-randomOffset, randomOffset)
    local offsetY = Rnumber(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)

    -- 第二次点击（重新计算偏移）
    offsetX = Rnumber(-randomOffset, randomOffset)
    offsetY = Rnumber(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)
  end
end

-- 为了保持兼容性，保留原函数名
DClick = doubleClick

-- 多色查找并双击函数（使用区域参数）
function findColorAndDoubleClickInRegion(colorData, randomOffset, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 从颜色数据中提取区域参数
  local regionData = colorData[1]
  local x1 = regionData[2]
  local y1 = regionData[3]
  local x2 = regionData[4]
  local y2 = regionData[5]

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  -- 如果找到颜色则执行双击
  if foundX ~= -1 then
    DClick(foundX, foundY, randomOffset)
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_DClick = findColorAndDoubleClickInRegion

-- 颜色比较并点击函数
function compareColorAndClick(colorData, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end

  -- 检测多色
  local colorFound = multiColor(colorData[2], similarity)

  if colorFound then
    -- 检查是否需要点击
    local shouldClick = colorData[1][2]
    if shouldClick then
      local clickX = colorData[2][1][1]
      local clickY = colorData[2][1][2]
      _tap(clickX, clickY)
    end

    -- 打印日志
    local logMessage = colorData[1][1]
    _print(logMessage)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
_cmp = compareColorAndClick

-- 颜色比较并带偏移点击函数
function compareColorAndClickWithOffset(colorData, offsetX, offsetY)
  -- 设置默认偏移量
  if not offsetX then
    offsetX = 0
  end
  if not offsetY then
    offsetY = 0
  end

  -- 检测多色（固定相似度90）
  local colorFound = multiColor(colorData[2], 90)

  if colorFound then
    -- 检查是否需要点击
    local shouldClick = colorData[1][2]
    if shouldClick then
      local clickX = colorData[2][1][1] + offsetX
      local clickY = colorData[2][1][2] + offsetY
      _tap(clickX, clickY)
    end

    -- 打印日志
    local logMessage = colorData[1][1]
    _print(logMessage)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
_cmp_p = compareColorAndClickWithOffset

-- 循环颜色比较并点击函数
function compareColorAndClickLoop(colorData, loopConfig)
  -- loopConfig[1] = 循环次数, loopConfig[2] = 每次循环间隔时间
  local maxLoops = loopConfig[1]
  local sleepInterval = loopConfig[2]

  for i = 1, maxLoops do
    -- 检测多色（固定相似度90）
    local colorFound = multiColor(colorData[2], 90)

    if colorFound then
      -- 检查是否需要点击
      local shouldClick = colorData[1][2]
      if shouldClick then
        local clickX = colorData[2][1][1]
        local clickY = colorData[2][1][2]
        _tap(clickX, clickY)
      end

      -- 打印日志
      local logMessage = colorData[1][1]
      _print(logMessage)
      return true
    end

    -- 等待指定时间后继续下一次循环
    mSleep(sleepInterval)
  end

  return false
end

-- 为了保持兼容性，保留原函数名
_cmp_cx = compareColorAndClickLoop

-- 表格颜色比较并点击函数
function compareColorTableAndClick(colorDataTable, similarity, offsetX, offsetY)
  -- 设置默认参数
  if not similarity then
    similarity = 90
  end
  if not offsetX then
    offsetX = 0
  end
  if not offsetY then
    offsetY = 0
  end

  -- 保持屏幕截图以提高性能
  keepScreen(true)

  -- 遍历颜色数据表格
  for index, colorData in pairs(colorDataTable) do
    -- 检测多色
    local colorFound = multiColor(colorData[2], similarity, false)

    if colorFound then
      -- 检查是否需要点击
      local shouldClick = colorData[1][2]
      if shouldClick then
        local clickX = colorData[2][1][1]
        local clickY = colorData[2][1][2]
        _tap(clickX, clickY)
      end

      -- 打印日志
      local logMessage = colorData[1][1]
      _print(logMessage)

      -- 释放屏幕截图
      keepScreen(false)
      return true
    end
  end

  -- 释放屏幕截图
  keepScreen(false)
  return false
end

-- 为了保持兼容性，保留原函数名
_cmp_tb = compareColorTableAndClick

-- 循环表格颜色比较并点击函数
function compareColorTableAndClickLoop(colorDataTable, loopConfig, similarity, offsetX, offsetY)
  -- 设置默认参数
  if not similarity then
    similarity = 90
  end
  if not offsetX then
    offsetX = 0
  end
  if not offsetY then
    offsetY = 0
  end

  -- loopConfig[1] = 循环次数
  local maxLoops = loopConfig[1]

  for i = 1, maxLoops do
    -- 保持屏幕截图以提高性能
    keepScreen(true)

    -- 遍历颜色数据表格
    for index, colorData in pairs(colorDataTable) do
      -- 检测多色
      local colorFound = multiColor(colorData[2], similarity, false)

      if colorFound then
        -- 检查是否需要点击
        local shouldClick = colorData[1][2]
        if shouldClick then
          local clickX = colorData[2][1][1]
          local clickY = colorData[2][1][2]
          _tap(clickX, clickY)
        end

        -- 打印日志
        local logMessage = colorData[1][1]
        _print(logMessage)

        -- 释放屏幕截图
        keepScreen(false)
        return true
      end
    end

    -- 释放屏幕截图
    keepScreen(false)

    -- 随机等待20-150毫秒后继续下一次循环
    _Sleep(20, 150)
  end

  return false
end

-- 为了保持兼容性，保留原函数名
_cmp_tb_cx = compareColorTableAndClickLoop

-- 图像查找并点击函数
function findImageAndClick(imageData, offset, customRegion)
  -- 设置默认偏移量
  if not offset then
    offset = {0, 0}
  end

  local found = false
  local foundX = -1
  local foundY = -1

  if customRegion ~= nil then
    -- 使用自定义区域进行查找
    local colorInfo = imageData[3]
    local colorData = imageData[2][1]
    foundX, foundY = findMultiColorInRegionFuzzy(
      colorInfo[1], colorInfo[2], colorData,
      customRegion[1], customRegion[2], customRegion[3], customRegion[4]
    )
  else
    -- 使用默认区域进行查找
    local colorInfo = imageData[3]
    local colorData = imageData[2][1]
    local regionInfo = imageData[1]
    foundX, foundY = findMultiColorInRegionFuzzy(
      colorInfo[1], colorInfo[2], colorData,
      regionInfo[2], regionInfo[3], regionInfo[4], regionInfo[5]
    )
  end

  if foundX > -1 then
    -- 检查是否需要点击
    local shouldClick = imageData[2][2]
    if shouldClick then
      local clickType = imageData[2][3]
      local clickX = foundX + offset[1]
      local clickY = foundY + offset[2]

      if clickType == 1 then
        -- 单击
        _tap(clickX, clickY)
      else
        -- 双击
        _tap(clickX, clickY, 2)
      end
    end

    -- 打印日志
    local logMessage = imageData[1][1]
    _print(logMessage)
    found = true
  end

  return found, foundX, foundY
end

-- 为了保持兼容性，保留原函数名
_find = findImageAndClick

-- 循环图像查找并点击函数
function findImageAndClickLoop(imageData, loopConfig, offset, customRegion)
  -- 设置默认偏移量
  if not offset then
    offset = {0, 0}
  end

  local found = false
  local foundX = -1
  local foundY = -1

  -- loopConfig[1] = 循环次数, loopConfig[2] = 每次循环间隔时间
  local maxLoops = loopConfig[1]
  local sleepInterval = loopConfig[2]

  for i = 1, maxLoops do
    if customRegion ~= nil then
      -- 使用自定义区域进行查找
      local colorInfo = imageData[3]
      local colorData = imageData[2][1]
      foundX, foundY = findMultiColorInRegionFuzzy(
        colorInfo[1], colorInfo[2], colorData,
        customRegion[1], customRegion[2], customRegion[3], customRegion[4]
      )
    else
      -- 使用默认区域进行查找
      local colorInfo = imageData[3]
      local colorData = imageData[2][1]
      local regionInfo = imageData[1]
      foundX, foundY = findMultiColorInRegionFuzzy(
        colorInfo[1], colorInfo[2], colorData,
        regionInfo[2], regionInfo[3], regionInfo[4], regionInfo[5]
      )
    end

    if foundX > -1 then
      -- 检查是否需要点击
      local shouldClick = imageData[2][2]
      if shouldClick then
        local clickType = imageData[2][3]
        local clickX = foundX + offset[1]
        local clickY = foundY + offset[2]

        if clickType == 1 then
          -- 单击
          _tap(clickX, clickY)
        else
          -- 双击
          _tap(clickX, clickY, 2)
        end
      end

      -- 打印日志
      local logMessage = imageData[1][1]
      _print(logMessage)
      return true, foundX, foundY
    end

    -- 等待指定时间后继续下一次循环
    mSleep(sleepInterval)
  end

  return false, foundX, foundY
end

-- 为了保持兼容性，保留原函数名
_find_cx = findImageAndClickLoop

-- 表格图像查找并点击函数
function findImageTableAndClick(imageDataTable, offset, customRegion)
  -- 设置默认偏移量
  if not offset then
    offset = {0, 0}
  end

  local found = false
  local foundX = -1
  local foundY = -1

  -- 保持屏幕截图以提高性能
  keepScreen(true)

  if customRegion ~= nil then
    -- 使用自定义区域遍历图像表格
    for index, imageData in pairs(imageDataTable) do
      local colorInfo = imageData[3]
      local colorData = imageData[2][1]
      foundX, foundY = findMultiColorInRegionFuzzy(
        colorInfo[1], colorInfo[2], colorData,
        customRegion[1], customRegion[2], customRegion[3], customRegion[4]
      )

      if foundX > -1 then
        -- 检查是否需要点击
        local shouldClick = imageData[2][2]
        if shouldClick then
          local clickType = imageData[2][3]
          local clickX = foundX + offset[1]
          local clickY = foundY + offset[2]

          if clickType == 1 then
            -- 单击
            _tap(clickX, clickY)
          else
            -- 双击
            _tap(clickX, clickY, 2)
          end
        end

        -- 打印日志
        local logMessage = imageData[1][1]
        _print(logMessage)
        found = true
        break
      end
    end
  else
    -- 使用默认区域遍历图像表格
    for index, imageData in pairs(imageDataTable) do
      local colorInfo = imageData[3]
      local colorData = imageData[2][1]
      local regionInfo = imageData[1]
      foundX, foundY = findMultiColorInRegionFuzzy(
        colorInfo[1], colorInfo[2], colorData,
        regionInfo[2], regionInfo[3], regionInfo[4], regionInfo[5]
      )

      if foundX > -1 then
        -- 检查是否需要点击
        local shouldClick = imageData[2][2]
        if shouldClick then
          local clickType = imageData[2][3]
          local clickX = foundX + offset[1]
          local clickY = foundY + offset[2]

          if clickType == 1 then
            -- 单击
            _tap(clickX, clickY)
          else
            -- 双击
            _tap(clickX, clickY, 2)
          end
        end

        -- 打印日志
        local logMessage = imageData[1][1]
        _print(logMessage)
        found = true
        break
      end
    end
  end

  -- 释放屏幕截图
  keepScreen(false)
  return found, foundX, foundY
end

-- 为了保持兼容性，保留原函数名
_find_tb = findImageTableAndClick

-- 循环表格图像查找并点击函数
function findImageTableAndClickLoop(imageDataTable, loopConfig, offset, customRegion)
  -- 设置默认偏移量
  if not offset then
    offset = {0, 0}
  end

  local foundX = -1
  local foundY = -1

  -- loopConfig[1] = 循环次数, loopConfig[2] = 每次循环间隔时间
  local maxLoops = loopConfig[1]
  local sleepInterval = loopConfig[2]

  if customRegion ~= nil then
    -- 使用自定义区域进行循环查找
    for i = 1, maxLoops do
      keepScreen(true)

      for index, imageData in pairs(imageDataTable) do
        local colorInfo = imageData[3]
        local colorData = imageData[2][1]
        foundX, foundY = findMultiColorInRegionFuzzy(
          colorInfo[1], colorInfo[2], colorData,
          customRegion[1], customRegion[2], customRegion[3], customRegion[4]
        )

        if foundX > -1 then
          -- 检查是否需要点击
          local shouldClick = imageData[2][2]
          if shouldClick then
            local clickType = imageData[2][3]
            local clickX = foundX + offset[1]
            local clickY = foundY + offset[2]

            if clickType == 1 then
              -- 单击
              _tap(clickX, clickY)
            else
              -- 双击
              _tap(clickX, clickY, 2)
            end
          end

          -- 打印日志
          local logMessage = imageData[1][1]
          _print(logMessage)
          keepScreen(false)
          return true, foundX, foundY
        end
      end

      keepScreen(false)
      mSleep(sleepInterval)
    end
  else
    -- 使用默认区域进行循环查找
    for i = 1, maxLoops do
      keepScreen(true)

      for index, imageData in pairs(imageDataTable) do
        local colorInfo = imageData[3]
        local colorData = imageData[2][1]
        local regionInfo = imageData[1]
        foundX, foundY = findMultiColorInRegionFuzzy(
          colorInfo[1], colorInfo[2], colorData,
          regionInfo[2], regionInfo[3], regionInfo[4], regionInfo[5]
        )

        if foundX > -1 then
          -- 检查是否需要点击
          local shouldClick = imageData[2][2]
          if shouldClick then
            local clickType = imageData[2][3]
            local clickX = foundX + offset[1]
            local clickY = foundY + offset[2]

            if clickType == 1 then
              -- 单击
              _tap(clickX, clickY)
            else
              -- 双击
              _tap(clickX, clickY, 2)
            end
          end

          -- 打印日志
          local logMessage = imageData[1][1]
          _print(logMessage)
          keepScreen(false)
          return true, foundX, foundY
        end
      end

      keepScreen(false)
      mSleep(sleepInterval)
    end
  end

  keepScreen(false)
  return false, -1, -1
end

-- 为了保持兼容性，保留原函数名
_find_tb_cx = findImageTableAndClickLoop

-- 扩展图像查找函数（支持多方向查找和去重）
function findImageExtended(imageDataTable, searchRegion, loopConfig, orientation)
  -- 设置默认循环配置
  if not loopConfig then
    loopConfig = {1, 0}  -- 默认循环1次，间隔0毫秒
  end
  -- 设置默认方向
  if not orientation then
    orientation = 1
  end

  local resultList = {}
  local maxLoops = loopConfig[1]
  local sleepInterval = loopConfig[2]

  for i = 1, maxLoops do
    keepScreen(true)

    -- 遍历图像数据表格
    for index, imageData in pairs(imageDataTable) do
      local colorInfo = imageData[3]
      local colorData = imageData[2][1]
      local searchOptions = {orient = orientation}

      -- 使用扩展查找函数
      local foundResults = findMultiColorInRegionFuzzyExt(
        colorInfo[1], colorInfo[2], colorData,
        searchRegion[1], searchRegion[2], searchRegion[3], searchRegion[4],
        searchOptions
      )

      if #foundResults ~= 0 then
        -- 打印日志
        local logMessage = imageData[1][1]
        _print(logMessage)

        -- 将找到的结果添加到结果列表
        for _, result in pairs(foundResults) do
          resultList[#resultList + 1] = result
        end
      end
    end

    -- 如果找到结果则跳出循环
    if #resultList ~= 0 then
      keepScreen(false)
      break
    end

    mSleep(sleepInterval)
    keepScreen(false)
  end

  -- 对结果进行去重处理（移除Y坐标相近的重复项）
  if #resultList ~= 0 then
    -- 标记需要删除的项
    for i = 1, #resultList - 1 do
      for j = i + 1, #resultList do
        local yDiff = resultList[i].y - resultList[j].y
        if yDiff < 5 and yDiff > -5 then
          resultList[j].y = -1  -- 标记为删除
        end
      end
    end

    -- 从后往前删除标记的项
    for i = #resultList, 1, -1 do
      if resultList[i].y == -1 then
        table.remove(resultList, i)
      end
    end
  end

  return resultList
end

-- 为了保持兼容性，保留原函数名
_find_ex = findImageExtended

-- 过滤特殊字符函数（保留数字、字母和中文字符）
function filterSpecialCharacters(inputString)
  local resultTable = {}
  local position = 1

  while position <= #inputString do
    local byteValue = string.byte(inputString, position)

    if not byteValue then
      break
    end

    if byteValue < 192 then
      -- ASCII字符处理
      -- 保留数字(48-57)、大写字母(65-90)、小写字母(97-122)
      if (byteValue >= 48 and byteValue <= 57) or
         (byteValue >= 65 and byteValue <= 90) or
         (byteValue >= 97 and byteValue <= 122) then
        table.insert(resultTable, string.char(byteValue))
      end
      position = position + 1

    elseif byteValue < 224 then
      -- 2字节UTF-8字符，跳过
      position = position + 2

    elseif byteValue < 240 then
      -- 3字节UTF-8字符处理（主要是中文字符）
      if byteValue >= 228 and byteValue <= 233 then
        local byte2 = string.byte(inputString, position + 1)
        local byte3 = string.byte(inputString, position + 2)

        if byte2 and byte3 then
          local minByte2 = 128
          local maxByte2 = 191
          local minByte3 = 128
          local maxByte3 = 191

          -- 特殊处理某些中文字符范围
          if byteValue == 228 then
            minByte2 = 184  -- 调整第二字节范围
          elseif byteValue == 233 then
            local tempMax = 190
            if byte2 ~= 190 then
              maxByte3 = 191
            else
              maxByte3 = 165
            end
            maxByte2 = tempMax
          end

          -- 检查是否在有效的中文字符范围内
          if byte2 >= minByte2 and byte2 <= maxByte2 and
             byte3 >= minByte3 and byte3 <= maxByte3 then
            table.insert(resultTable, string.char(byteValue, byte2, byte3))
          end
        end
      end
      position = position + 3

    elseif byteValue < 248 then
      -- 4字节UTF-8字符，跳过
      position = position + 4
    elseif byteValue < 252 then
      -- 5字节UTF-8字符，跳过
      position = position + 5
    elseif byteValue < 254 then
      -- 6字节UTF-8字符，跳过
      position = position + 6
    else
      position = position + 1
    end
  end

  return table.concat(resultTable)
end

-- 为了保持兼容性，保留原函数名
_ENV["_过滤特殊字符"] = filterSpecialCharacters

-- 单点找色函数
function findSingleColor(color, x1, y1, x2, y2, similarity, shouldClick)
  -- 在指定区域查找单一颜色
  local foundX, foundY = findColorInRegionFuzzy(color, x1, y1, x2, y2, similarity)

  if foundX > -1 then
    -- 如果需要点击
    if shouldClick then
      _tap(foundX, foundY)
    end
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
_ENV["_单点找色"] = findSingleColor

-- Base64编码函数（读取文件并转换为Base64）
function readFileAndEncodeBase64(filePath)
  -- 打开文件并读取所有内容
  local file = io.open(filePath, "rb")
  local fileContent = file:read("*all")
  file:close()

  -- 位运算函数引用
  local bor = bit32.bor
  local band = bit32.band
  local lshift = bit32.lshift
  local rshift = bit32.rshift

  -- Base64字符表
  local base64Chars = {
    "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P",
    "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f",
    "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
    "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "+", "/"
  }

  -- Base64编码实现
  local result = {}
  local padding = ""
  local len = #fileContent

  -- 处理每3个字节为一组进行Base64编码
  for i = 1, len, 3 do
    local byte1 = string.byte(fileContent, i) or 0
    local byte2 = string.byte(fileContent, i + 1) or 0
    local byte3 = string.byte(fileContent, i + 2) or 0

    -- 将3个字节转换为4个Base64字符
    local combined = lshift(byte1, 16) + lshift(byte2, 8) + byte3

    local char1 = base64Chars[rshift(combined, 18) + 1]
    local char2 = base64Chars[band(rshift(combined, 12), 63) + 1]
    local char3 = base64Chars[band(rshift(combined, 6), 63) + 1]
    local char4 = base64Chars[band(combined, 63) + 1]

    table.insert(result, char1)
    table.insert(result, char2)

    -- 处理填充
    if i + 1 <= len then
      table.insert(result, char3)
    else
      table.insert(result, "=")
    end

    if i + 2 <= len then
      table.insert(result, char4)
    else
      table.insert(result, "=")
    end
  end

  return table.concat(result)
end

-- 为了保持兼容性，保留原函数名
_ReadBase64 = readFileAndEncodeBase64

-- 脚本调用函数（显示图片并处理用户交互）
function callScript(imageName)
  -- 显示窗口
  fwShowWnd("wid", 660, 200, 1375, 905, 1)

  -- 显示按钮和图片
  local imageFile = imageName .. ".png"
  fwShowButton("wid", "vid", "", "", "", imageFile, 15, 0, 0, 600, 600)

  local lastActionTime = nil
  local startTime = os.time()

  while true do
    -- 检查按钮是否被按下
    local pressedButton = fwGetPressedButton()
    if pressedButton == "vid" then
      fwCloseWnd("wid")
      playAudio("")
      mSleep(2000)
      return true
    end

    -- 处理震动和音频提示
    local currentTime = os.time()
    local timeSinceLastAction = lastActionTime and (currentTime - lastActionTime) or 0

    if UI_msg == "震动提示" then
      -- 震动提示模式
      if timeSinceLastAction > 1 then
        vibrator()
        lastActionTime = currentTime
      end
    else
      -- 音频提示模式
      if timeSinceLastAction > 30 then
        playAudio("634.wav")
        lastActionTime = currentTime
      end
    end

    -- 检查超时（5分钟）
    local totalElapsed = currentTime - startTime
    if totalElapsed > 300 then
      if imageName == "dd_zw" then
        -- 账务超时
        fwCloseWnd("wid")
        playAudio("")
        mSleep(2000)
        return false
      elseif imageName == "dd_7" then
        -- 旗子处理超时
        fwCloseWnd("wid")
        playAudio("")
        closeApp("com.netease.mhxyhtb")
        dialog("旗子处理提示超过5分钟,自动关闭游戏！", time)
        lua_exit()
        mSleep(10)
      elseif imageName == "dd_wl" then
        -- 网络超时
        fwCloseWnd("wid")
        playAudio("")
        closeApp("com.netease.mhxyhtb")
        dialog("网络正常,无法通讯服务器,提示超过5分钟,自动关闭游戏！", time)
        lua_exit()
        mSleep(10)
      end
    end

    mSleep(200)
  end
end

-- 为了保持兼容性，保留原函数名
_call_script = callScript

-- 图像查找函数（在指定区域查找图片）
function findImage(imagePath, x1, y1, x2, y2, shouldClick, similarity)
  -- 在指定区域查找图像
  local foundX, foundY = findImageInRegionFuzzy(
    imagePath, similarity, x1, y1, x2, y2, 0
  )

  if foundX > -1 then
    -- 如果需要点击
    if shouldClick then
      _tap(foundX, foundY)
    end

    -- 打印日志
    _print(imagePath)
    return true, foundX, foundY
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
_findImg = findImage

-- 表格查找函数（在指定区域查找表格中的颜色）
function findTableInRegion(colorDataTable, searchRegion)
  local foundX = nil
  local foundY = nil

  -- 遍历颜色数据表格
  for index, colorData in pairs(colorDataTable) do
    -- 在指定区域查找多色（固定相似度85）
    foundX, foundY = findMultiColorInRegionFuzzyByTable(
      colorData[2], 85,
      searchRegion[1], searchRegion[2], searchRegion[3], searchRegion[4]
    )

    if foundX > -1 then
      -- 点击找到的位置
      _tap(foundX, foundY)

      -- 打印日志
      _print(colorData[1])
      return true
    end
  end

  return false
end

-- 为了保持兼容性，保留原函数名
p_find_tb = findTableInRegion

-- 读取文件字节数据函数
function readFileByte(filePath)
  -- 以二进制模式打开文件
  local file = io.open(filePath, "rb")
  -- 读取所有内容
  local content = file:read("*all")
  -- 关闭文件
  file:close()
  return content
end

-- 为了保持兼容性，保留原函数名
ReadFileByte = readFileByte

-- 合并表格函数
function mergeTable(table1, table2)
  local result = {}

  -- 添加第一个表格的所有元素（使用ipairs保持顺序）
  for index, value in ipairs(table1) do
    result[#result + 1] = value
  end

  -- 添加第二个表格的所有元素（使用pairs遍历所有键值对）
  for key, value in pairs(table2) do
    result[#result + 1] = value
  end

  return result
end

-- 为了保持兼容性，保留原函数名
_ENV["_合并table"] = mergeTable

-- 表格坐标去重函数（移除相同坐标的重复项）
function removeTableDuplicateCoordinates(coordinateTable)
  -- 标记重复的坐标
  for i = 1, #coordinateTable - 1 do
    for j = i + 1, #coordinateTable do
      local coord1 = coordinateTable[i]
      local coord2 = coordinateTable[j]

      -- 如果坐标相同，标记第二个为删除
      if coord1.game_x == coord2.game_x and coord1.game_y == coord2.game_y then
        coord2.game_x = -1
      end
    end
  end

  -- 从后往前删除标记的项
  for i = #coordinateTable, 1, -1 do
    if coordinateTable[i].game_x == -1 then
      table.remove(coordinateTable, i)
    end
  end
end

-- 为了保持兼容性，保留原函数名
_ENV["_table坐标去重"] = removeTableDuplicateCoordinates

-- 表格合并去重函数（合并两个表格并移除Y坐标相近的重复项）
function mergeTableAndRemoveDuplicates(table1, table2)
  local result = {}

  -- 添加第一个表格的所有元素
  for index, value in ipairs(table1) do
    result[#result + 1] = value
  end

  -- 添加第二个表格的所有元素
  for key, value in pairs(table2) do
    result[#result + 1] = value
  end

  -- 去重处理：标记Y坐标相近的重复项（差值在5像素内）
  local totalCount = #result
  for i = 1, totalCount - 1 do
    for j = i + 1, totalCount do
      local yDiff = result[i].y - result[j].y
      if yDiff < 5 and yDiff > -5 then
        result[j].y = -1  -- 标记为删除
      end
    end
  end

  -- 从后往前删除标记的项
  for i = #result, 1, -1 do
    if result[i].y == -1 then
      table.remove(result, i)
    end
  end

  return result
end

-- 为了保持兼容性，保留原函数名
_ENV["_table合并去重"] = mergeTableAndRemoveDuplicates

-- 表格合并去重函数（游戏坐标版本）
function mergeTableAndRemoveDuplicatesGame(table1, table2)
  local result = {}

  -- 添加第一个表格的所有元素
  for index, value in ipairs(table1) do
    result[#result + 1] = value
  end

  -- 添加第二个表格的所有元素
  for key, value in pairs(table2) do
    result[#result + 1] = value
  end

  -- 去重处理：标记相同游戏坐标的重复项
  for i = 1, #result - 1 do
    for j = i + 1, #result do
      local coord1 = result[i]
      local coord2 = result[j]

      -- 如果游戏坐标完全相同，标记第二个为删除
      if coord1.game_x == coord2.game_x and coord1.game_y == coord2.game_y then
        coord2.game_y = -1
      end
    end
  end

  -- 从后往前删除标记的项
  for i = #result, 1, -1 do
    if result[i].game_y == -1 then
      table.remove(result, i)
    end
  end

  return result
end

-- 为了保持兼容性，保留原函数名
_ENV["_table合并去重g"] = mergeTableAndRemoveDuplicatesGame

-- 随机睡眠函数
function randomMSleep(minTime, maxTime)
  local sleepTime = math.random(minTime, maxTime)
  mSleep(sleepTime)
end

-- 为了保持兼容性，保留原函数名
My_msleep = randomMSleep

-- 调试打印函数
function debugPrint(message)
  -- 如果启用了nLog调试
  if nLog_debug then
    nLog(message)
  end

  -- 如果启用了toast调试
  if toast_debug then
    toast(message, 1)
  end
end

-- 为了保持兼容性，保留原函数名
printf = debugPrint

-- 多色查找并点击函数（扩展版）
function findColorAndClickExtended(colorData, x1, y1, x2, y2, randomOffset, shouldClick, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  if foundX ~= -1 then
    -- 检查是否需要点击（默认为true）
    if shouldClick == nil or shouldClick == true then
      -- 这里原代码有点击逻辑但被注释掉了
      -- 可能需要根据实际需求添加点击功能
    end

    -- 打印日志（如果消息不为空）
    local logMessage = colorData[1]
    if logMessage ~= "" then
      printf(logMessage)
    end

    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_Clicke = findColorAndClickExtended

-- 随机数生成函数（重新定义，因为之前可能被覆盖）
function generateRandomNumber(min, max)
  return math.random(min, max)
end

-- 为了保持兼容性，保留原函数名
Rnumber = generateRandomNumber

-- 点击函数（带随机偏移）
function clickWithRandomOffset(x, y, randomOffset)
  -- 设置默认随机偏移量
  if randomOffset == nil then
    randomOffset = 5
    -- 使用默认偏移量5
    local offsetX = Rnumber(-5, randomOffset)
    local offsetY = Rnumber(-5, randomOffset)
    x = x + offsetX
    y = y + offsetY
  else
    -- 使用自定义偏移量
    local offsetX = Rnumber(-randomOffset, randomOffset)
    local offsetY = Rnumber(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY
  end

  -- 执行点击操作
  touchDown(1, x, y)
  mSleep(Rnumber(30, 200))
  touchUp(1, x, y)
end

-- 为了保持兼容性，保留原函数名
Click = clickWithRandomOffset

-- 多色查找并点击函数（使用区域参数）
function findColorAndClickWithRegion(colorData, randomOffset, shouldClick, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 从颜色数据中提取区域参数
  local regionData = colorData[1]
  local x1 = regionData[2]
  local y1 = regionData[3]
  local x2 = regionData[4]
  local y2 = regionData[5]

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  if foundX ~= -1 then
    -- 检查是否需要点击（默认为true）
    if shouldClick == nil or shouldClick == true then
      Click(foundX, foundY, randomOffset)
    end
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_Click = findColorAndClickWithRegion

-- 多色查找函数（仅查找不点击）
function findColorOnly(colorData, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end

  -- 从颜色数据中提取区域参数
  local regionData = colorData[1]
  local x1 = regionData[2]
  local y1 = regionData[3]
  local x2 = regionData[4]
  local y2 = regionData[5]

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  -- 如果找到则打印日志
  if foundX ~= -1 then
    local logMessage = colorData[1][1]
    printf(logMessage)
  end

  return foundX, foundY
end

-- 为了保持兼容性，保留原函数名
FC = findColorOnly

-- 时间处理函数2（点击并记录时间）
function timeHandler2(x, y)
  -- 添加随机偏移
  local randomOffset = math.random(-1, 1)

  -- 执行点击操作
  touchDown(1, x + randomOffset, y)
  mSleep(math.random(100, 500))
  touchUp(1, x + randomOffset, y)

  -- 记录当前时间到全局变量
  local currentTime = os.time()
  _ENV["卡点时间"] = currentTime
end

-- 为了保持兼容性，保留原函数名
d2 = timeHandler2

-- 时间处理函数1（快速点击并记录时间）
function timeHandler1(x, y)
  -- 添加随机偏移
  local randomOffset = math.random(-1, 1)

  -- 执行快速点击操作
  touchDown(1, x + randomOffset, y + randomOffset)
  mSleep(math.random(10, 20))
  touchUp(1, x + randomOffset, y + randomOffset)

  -- 记录当前时间到全局变量
  local currentTime = os.time()
  _ENV["卡点时间"] = currentTime
end

-- 为了保持兼容性，保留原函数名
d1 = timeHandler1

-- 时间处理函数（带范围的随机点击）
function timeHandler(minX, minY, maxX, maxY)
  -- 设置默认范围（如果没有提供最大值，使用最小值）
  if not maxX then
    maxX = minX
  end
  if not maxY then
    maxY = minY
  end

  -- 生成随机坐标
  local randomX = math.random(minX, maxX)
  local randomY = math.random(minY, maxY)

  -- 添加额外的随机偏移
  local extraOffset = math.random(-1, 1)

  -- 预延迟
  mSleep(math.random(10, 50))

  -- 执行点击操作
  touchDown(1, randomX + extraOffset, randomY + extraOffset)
  mSleep(math.random(10, 50))
  touchUp(1, randomX + extraOffset, randomY + extraOffset)

  -- 记录当前时间到全局变量
  local currentTime = os.time()
  _ENV["卡点时间"] = currentTime
end

-- 为了保持兼容性，保留原函数名
d = timeHandler
