local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1
L0_1 = require
L1_1 = "sz"
L0_1 = L0_1(L1_1)
L1_1 = L0_1.json
L2_1 = require
L3_1 = "socket"
L2_1 = L2_1(L3_1)
L3_1 = require
L4_1 = "szocket.http"
L3_1 = L3_1(L4_1)
L4_1 = require
L5_1 = "mime"
L4_1 = L4_1(L5_1)
SERVER_HOST = "http://**************:5555/"
CARD_NUM = "H6H5DATVGEZLD8KHTSVU"
AUTH_KEY = "CAW5F6CUL6G8RCSALYV9"
time = 0
image_dir = "/var/mobile/Media/TouchSprite/res/"

function L5_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L4_2 = SERVER_HOST
  L5_2 = A1_2
  L4_2 = L4_2 .. L5_2
  L5_2 = {}
  L6_2 = SCRECT_KEY
  if L6_2 == nil then
    L6_2 = {}
    L6_2.card_num = A0_2
    L7_2 = AUTH_KEY
    L6_2.auth_key = L7_2
    L5_2 = L6_2
  else
    L6_2 = string
    L6_2 = L6_2.format
    L7_2 = "%s_%s"
    L8_2 = A0_2
    L9_2 = L2_1.gettime
    L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L9_2()
    L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
    L8_2 = L6_2
    L7_2 = L6_2.aes128_encrypt
    L9_2 = SCRECT_KEY
    L7_2 = L7_2(L8_2, L9_2)
    L8_2 = L7_2
    L7_2 = L7_2.base64_encode
    L7_2 = L7_2(L8_2)
    L8_2 = {}
    L9_2 = urlEncoder
    L10_2 = L7_2
    L9_2 = L9_2(L10_2)
    L8_2.card_num = L9_2
    L9_2 = AUTH_KEY
    L8_2.auth_key = L9_2
    L8_2.secret_key = 1
    L5_2 = L8_2
  end
  if A2_2 ~= nil then
    L6_2 = io
    L6_2 = L6_2.open
    L7_2 = A2_2
    L8_2 = "rb"
    L6_2 = L6_2(L7_2, L8_2)
    if L6_2 then
      L8_2 = L6_2
      L7_2 = L6_2.read
      L9_2 = "*a"
      L7_2 = L7_2(L8_2, L9_2)
      L9_2 = L6_2
      L8_2 = L6_2.close
      L8_2(L9_2)
      L8_2 = L4_1.b64
      L9_2 = L7_2
      L8_2 = L8_2(L9_2)
      L9_2 = urlEncoder
      L10_2 = L8_2
      L9_2 = L9_2(L10_2)
      L5_2.image = L9_2
    else
      L7_2 = {}
      L7_2.err_code = 1023
      L7_2.err_msg = "Failed to open image file"
      return L7_2
    end
  end
  if A3_2 ~= nil then
    L6_2 = pairs
    L7_2 = A3_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L5_2[L9_2] = L10_2
    end
  end
  L6_2 = ""
  L7_2 = pairs
  L8_2 = L5_2
  L7_2, L8_2, L9_2 = L7_2(L8_2)
  for L10_2, L11_2 in L7_2, L8_2, L9_2 do
    L12_2 = L6_2
    L13_2 = L10_2
    L14_2 = "="
    L15_2 = L11_2
    L16_2 = "&"
    L6_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2
  end
  L7_2 = {}
  L8_2 = L3_1.request
  L9_2 = {}
  L9_2.method = "POST"
  L9_2.url = L4_2
  L10_2 = {}
  L10_2.Expect = ""
  L10_2["Content-Type"] = "application/x-www-form-urlencoded"
  L11_2 = #L6_2
  L10_2["Content-Length"] = L11_2
  L9_2.headers = L10_2
  L10_2 = ltn12
  L10_2 = L10_2.source
  L10_2 = L10_2.string
  L11_2 = L6_2
  L10_2 = L10_2(L11_2)
  L9_2.source = L10_2
  L10_2 = ltn12
  L10_2 = L10_2.sink
  L10_2 = L10_2.table
  L11_2 = L7_2
  L10_2 = L10_2(L11_2)
  L9_2.sink = L10_2
  L8_2, L9_2 = L8_2(L9_2)
  if L9_2 == 200 then
    L10_2 = L1_1.decode
    L11_2 = L7_2[1]
    return L10_2(L11_2)
  else
    L10_2 = {}
    L10_2.err_code = 1354
    L10_2.err_msg = "No response from server"
    return L10_2
  end
end

postData = L5_1

function L5_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L0_2 = {}
  L1_2 = "0104441190e643f918e4c19f07f81fe0e4cf91a643190c441010400000007ff9fff318cc603308c823668f1bfffffe63018c07ffffffc002$关闭$203$14$32"
  L0_2[1] = L1_2
  L1_2 = addTSOcrDictEx
  L2_2 = L0_2
  L1_2 = L1_2(L2_2)
  L2_2 = 1
  L3_2 = 3
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = tsFindText
    L7_2 = L1_2
    L8_2 = "关闭"
    L9_2 = 254
    L10_2 = 17
    L11_2 = 506
    L12_2 = 228
    L13_2 = "C7C9C8 , 393738"
    L14_2 = 81 - L5_2
    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    if 0 < L6_2 then
      L8_2 = 0
      while true do
        L8_2 = L8_2 + 1
        L9_2 = snapshot
        L10_2 = "VER.png"
        L11_2 = L6_2 - 50
        L12_2 = L7_2 + 30
        L13_2 = L6_2 + 440
        L14_2 = L7_2 + 200
        L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
        L9_2 = postData
        L10_2 = CARD_NUM
        L11_2 = "4002"
        L12_2 = image_dir
        L13_2 = "VER.png"
        L12_2 = L12_2 .. L13_2
        L9_2 = L9_2(L10_2, L11_2, L12_2)
        L10_2 = L9_2.err_code
        if 0 < L10_2 then
          L10_2 = nLog
          L11_2 = "Call failed: "
          L12_2 = L9_2.err_msg
          L11_2 = L11_2 .. L12_2
          L10_2(L11_2)
          break
        end
        L10_2 = L9_2.ret
        L11_2 = L10_2[1]
        if L11_2 then
          L11_2 = nLog
          L12_2 = "Succeed: "
          L13_2 = [[

No: ]]
          L14_2 = L10_2[2]
          L15_2 = [[

Pos: ]]
          L16_2 = L10_2[3]
          L16_2 = L16_2[1]
          L17_2 = ", "
          L18_2 = L10_2[3]
          L18_2 = L18_2[2]
          L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2
          L11_2(L12_2)
          L11_2 = nLog
          L12_2 = "Done,click the correct one"
          L11_2(L12_2)
          L11_2 = tap
          L12_2 = L6_2 - 50
          L13_2 = L10_2[3]
          L13_2 = L13_2[1]
          L12_2 = L12_2 + L13_2
          L13_2 = L7_2 + 30
          L14_2 = L10_2[3]
          L14_2 = L14_2[2]
          L13_2 = L13_2 + L14_2
          L11_2(L12_2, L13_2)
          L11_2 = mSleep
          L12_2 = math
          L12_2 = L12_2.random
          L13_2 = 350
          L14_2 = 400
          L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2 = L12_2(L13_2, L14_2)
          L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
          break
        end
        if 5 <= L8_2 then
          L11_2 = dialog
          L12_2 = "验证失败"
          L13_2 = 1
          L11_2(L12_2, L13_2)
          do return end
          break
        end
        if L8_2 <= 2 then
          L11_2 = mSleep
          L12_2 = 1000
          L11_2(L12_2)
          L11_2 = _print
          L12_2 = "朝向验证：返回 -1, "
          L13_2 = L8_2
          L14_2 = "次！"
          L12_2 = L12_2 .. L13_2 .. L14_2
          L11_2(L12_2)
          goto lbl_115
          break
        end
        L11_2 = mSleep
        L12_2 = 1000
        L11_2(L12_2)
        L11_2 = _print
        L12_2 = "朝向验证：返回 -1, "
        L13_2 = L8_2
        L14_2 = "次,TOW"
        L12_2 = L12_2 .. L13_2 .. L14_2
        L11_2(L12_2)
        ::lbl_115::
      end
    end
  end
end

_ENV["抓鬼朝向"] = L5_1

function L5_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L0_2 = 0
  L1_2 = 0
  L2_2 = 0
  L3_2 = 0
  L4_2 = os
  L4_2 = L4_2.time
  L4_2 = L4_2()
  L5_2 = ocrInfo
  L6_2 = "haoi23"
  L7_2 = "npgjzx0762100514"
  L8_2 = "npgjzx0762100514|FE06DC2E92AE0929|p:t"
  L5_2(L6_2, L7_2, L8_2)
  while true do
    L5_2 = TextLog
    L6_2 = "Suanst请求中...！"
    L7_2 = "/"
    L8_2 = L0_2
    L9_2 = "/"
    L10_2 = L1_2
    L11_2 = "/"
    L12_2 = L2_2
    L13_2 = "/"
    L14_2 = L3_2
    L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
    L7_2 = 4
    L8_2 = _ENV["提示字体大小"]
    L9_2 = _ENV["字体颜色"]
    L10_2 = _ENV["提示颜色"]
    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
    L5_2 = ocrScreen
    L6_2 = 311
    L7_2 = 56
    L8_2 = 923
    L9_2 = 370
    L10_2 = "T6001"
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
    if L5_2 then
      L6_2 = TextLog
      L7_2 = "Suanst请求成功"
      L8_2 = "/"
      L9_2 = L0_2
      L10_2 = "/"
      L11_2 = L1_2
      L12_2 = "/"
      L13_2 = L2_2
      L14_2 = "/"
      L15_2 = L3_2
      L7_2 = L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
      L8_2 = 4
      L9_2 = _ENV["提示字体大小"]
      L10_2 = _ENV["字体颜色"]
      L11_2 = _ENV["提示颜色"]
      L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
      L6_2 = my_Split
      L7_2 = L5_2
      L8_2 = ","
      L6_2 = L6_2(L7_2, L8_2)
      L7_2 = tonumber
      L8_2 = L6_2[1]
      L7_2 = L7_2(L8_2)
      L8_2 = tonumber
      L9_2 = 311
      L8_2 = L8_2(L9_2)
      L7_2 = L7_2 + L8_2
      L8_2 = tonumber
      L9_2 = L6_2[2]
      L8_2 = L8_2(L9_2)
      L9_2 = tonumber
      L10_2 = 59
      L9_2 = L9_2(L10_2)
      L8_2 = L8_2 + L9_2
      L9_2 = Click
      L10_2 = L7_2
      L11_2 = L8_2
      L12_2 = 0
      L9_2(L10_2, L11_2, L12_2)
      break
    end
    L6_2 = os
    L6_2 = L6_2.time
    L6_2 = L6_2()
    L6_2 = L6_2 - L4_2
    if 15 < L6_2 then
      return
    end
    L6_2 = mSleep
    L7_2 = 500
    L6_2(L7_2)
  end
end

Suanst = L5_1
